<div class="municipality-selector">
    <select
        id="municipality-select"
        name="municipality"
        title="Gemeinde auswählen"
        onchange="onMunicipalityChange(this.value)"
    >
        <option value="">2. Gemeinde auswählen...</option>
        {% for municipality in municipalities %}
        <option value="{{ municipality.id }}"
                {% if not municipality.has_data %}class="unavailable" disabled{% endif %}>
            {{ municipality.name }}{% if not municipality.has_data %} (keine Daten){% endif %}
        </option>
        {% endfor %}
    </select>

    {% if municipalities %}
    <div class="municipality-count">
        {% set available_count = municipalities|selectattr("has_data")|list|length %}
        {% set total_count = municipalities|length %}
        <span class="count-value">{{ available_count }}</span> von {{ total_count }} Gemeinden verfügbar
        {% if data_source %}
        <span class="data-source-info">für gewählte Datenquelle</span>
        {% endif %}
    </div>
    {% else %}
    <div class="no-municipalities">
        Keine Gemeinden gefunden
    </div>
    {% endif %}
</div>

<script>
function onMunicipalityChange(municipalityId) {
    const statusMessage = document.getElementById('query-status-message');
    const showZonesButton = document.getElementById('show-municipality-zones-btn');

    if (municipalityId) {
        // Get municipality name from the selected option
        const selectElement = document.getElementById('municipality-select');
        const selectedOption = selectElement.options[selectElement.selectedIndex];
        const municipalityName = selectedOption.text;

        // Hide status message and show button
        statusMessage.style.display = 'none';
        if (showZonesButton) {
            showZonesButton.classList.remove('hidden');
            showZonesButton.textContent = `Zonen für ${municipalityName} anzeigen`;
        }

        // Store selected municipality globally
        window.selectedMunicipality = municipalityId;

        // Clear previous results
        clearResults();

        // Ensure button handlers are attached
        if (typeof window.setupButtonHandlers === 'function') {
            window.setupButtonHandlers();
        }

    } else {
        // Show status message and hide button
        statusMessage.style.display = 'block';
        statusMessage.textContent = 'Select a municipality to begin';
        if (showZonesButton) {
            showZonesButton.classList.add('hidden');
        }

        window.selectedMunicipality = null;
        clearResults();

        // Clear zones from map when no municipality is selected
        if (typeof window.clearZones === 'function') {
            window.clearZones();
        }
    }
}

function clearResults() {
    const zonesTable = document.getElementById('zones-table');
    const zonesSummary = document.getElementById('zones-summary');
    const zonesGeoJson = document.getElementById('zones-geojson');
    const resultsSummary = document.getElementById('results-summary');

    if (zonesTable) zonesTable.innerHTML = '<p class="placeholder">Select a municipality and click "Show Zones" to see results</p>';
    if (zonesSummary) zonesSummary.innerHTML = '<p class="placeholder">Zone summary will appear here</p>';
    if (zonesGeoJson) zonesGeoJson.innerHTML = '<p class="placeholder">GeoJSON data will appear here</p>';
    if (resultsSummary) resultsSummary.textContent = 'No zones loaded';
}
</script>

<style>
.municipality-selector select option.unavailable {
    color: #999;
    font-style: italic;
}

.municipality-selector select option:disabled {
    color: #ccc;
}
</style>
